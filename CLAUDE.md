# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Game Server Checker Tool** - a PyQt6-based Windows application that automatically manages game server configurations for a Chinese web game platform. The tool monitors game server APIs and updates local `server.ini` configuration files to ensure players connect to the optimal servers.

**Core Business Logic:**
This tool is specifically designed for a game that uses **PID (Platform ID) and GID (Game ID)** pairs to identify different game instances. The core workflow is:

1. **API Monitoring**: Fetches server data from `https://api.t5game.5jli.com/serverselect/loginserverdata/` using PID/GID pairs
2. **Server Analysis**: Analyzes server opening times and selects servers that will open on specific future dates (typically tomorrow)
3. **Configuration Updates**: Updates local `server.ini` files with optimal server configurations
4. **Automatic Management**: Handles server lifecycle - adds new servers and disables old ones based on configurable criteria

**Key Features:**
- **PID/GID Configuration Management**: Per-file configuration of multiple PID/GID pairs
- **Smart Server Selection**: Finds servers opening N days in the future (configurable offset)
- **Automatic Server Lifecycle**: Disables servers older than X days (default 26 days)
- **Batch File Processing**: Multi-threaded processing of multiple `server.ini` files
- **GameClient.exe Integration**: Start/stop/restart game client processes on schedule
- **Advanced Business Rules**: Configurable conditions like `is_gain_reward=0` and `bid_mode=1`

## Development Commands

### Running the Application
```bash
# Run the main application
python source_code/main_pyqt6.py

# Check Python version compatibility
python --version
```

### Building Distribution
```bash
# Build executable using PyInstaller (optimized configuration)
pyinstaller GameServerChecker.spec

# The spec file is heavily optimized for size and performance:
# - Excludes unnecessary Qt modules and libraries
# - Removes debugging and testing frameworks
# - Enables UPX compression and symbol stripping
```

### Dependencies Management
```bash
# Install required dependencies
pip install -r requirements.txt

# Core dependencies include:
# - PyQt6>=6.9.0 (GUI framework)
# - pillow>=10.0.0 (image processing)
# - psutil>=5.9.0 (system monitoring)
# - ntplib>=0.4.0 (network time protocol)
# - pywin32>=306 (Windows API integration)
# - requests>=2.25.0 (HTTP client)
```

## Architecture Overview

### Core Structure
The application follows a **modular service-oriented architecture** with clear separation of concerns:

```
source_code/
├── main_pyqt6.py           # Application entry point
├── core/                   # Core business logic
│   ├── app_pyqt6.py       # Main application class with StartupManager
│   ├── config.py          # Configuration management (AppConfig)
│   ├── constants.py       # Application constants
│   ├── *_manager.py       # Specialized managers (file, schedule, upload, etc.)
│   └── ui_coordinator.py  # UI state coordination
├── services/              # Service layer
│   ├── unified_network_service.py    # Network operations with connection pooling
│   ├── optimized_file_service.py     # File processing with performance optimization
│   ├── configuration_service.py      # Enhanced configuration management
│   ├── file_processing_service.py    # File processing workflow
│   └── qt_*_service.py               # Qt-specific services (scheduler, tray)
├── gui/                   # UI components
│   ├── components_pyqt6.py          # Core UI components
│   ├── dialogs_pyqt6.py            # Dialog windows
│   ├── styles_pyqt6.py             # Styling and themes
│   └── ui_*.py                      # UI utilities and factories
└── utils/                 # Utility modules
    ├── pyqt6_threading.py          # Thread management (PyQt6ThreadManager, SafeUIUpdater)
    ├── performance_optimizer.py     # Memory and performance monitoring
    ├── sequential_executor.py       # Step-by-step operation execution
    ├── process_control.py          # GameClient.exe process management
    ├── dpi_adapter.py              # High DPI support
    └── system.py                   # System integration utilities
```

### Key Design Patterns

**1. Service Coordination Pattern**
- `UICoordinator` manages all UI state and interactions
- Service classes handle specific domains (network, file, scheduler)
- Clear delegation pattern in main app class

**2. Async Startup Architecture**
- `StartupManager` handles background initialization
- Progressive loading with user feedback
- Separates critical path from non-essential setup

**3. Thread-Safe Operations**
- `PyQt6ThreadManager` for safe threading
- `SafeUIUpdater` for cross-thread UI updates  
- `ThreadSafeConnectionPool` for network operations

**4. Performance Optimization**
- `performance_monitor` decorator for bottleneck detection
- Memory management with `memory_manager`
- Lazy loading through `lazy_import_manager`

## Key Components

### Core Business Logic Implementation

**1. File Processing Pipeline** (`FileProcessingService`)
The main business workflow for each `server.ini` file:
```
Step 1: Validate file existence and permissions
Step 2: Load PID/GID pairs from file configuration  
Step 3: Collect API data for each PID/GID pair
Step 4: Find target servers (opening tomorrow/N days later)
Step 5: Handle old server disabling (older than cutoff days)
Step 6: Add new servers to configuration
Step 7: Update server.ini file with new configuration
```

**2. Server Selection Logic** (`AppHelpers.find_target_server`)
- Fetches server data from API: `https://api.t5game.5jli.com/serverselect/loginserverdata/?pid={pid}&version=99&IMEI=&platCode=37wan&gid={gid}&account=1`
- Analyzes `autoOpenTime` timestamps (UTC+8 timezone)
- Finds servers opening exactly N days from now (default: tomorrow)
- Validates servers meet business criteria (age, status, etc.)

**3. INI File Management** (`OptimizedFileService`)
- Smart encoding detection (UTF-8, GBK, GB2312)
- ConfigParser-based INI file reading/writing
- Template-based server entry generation using `CONTENT_TEMPLATE`
- Handles malformed INI files with format correction

**4. Configuration System**
- **AppConfig**: JSON-based per-file PID/GID mapping and advanced settings
- **ConfigurationService**: Enhanced config management with Qt signals
- **Business Rules**: Configurable server lifecycle and selection criteria
- **Template System**: Standard INI file content generation

**5. Business Rules Engine**
Advanced settings that control server selection and lifecycle:
```python
{
    "enable_disable_logic": True,           # Enable automatic server disabling
    "disable_days_cutoff": 26,              # Disable servers older than 26 days
    "disable_conditions_str": "is_gain_reward=0\nbid_mode=1",  # Conditions for disabling
    "find_server_days_offset": 1            # Look for servers opening N days later
}
```

**6. Server INI File Structure**
The tool manages `server.ini` files with this structure:
```ini
pid=7
gid=1003491
sid=12345
online_count=3
online_time=3
is_offline=1
online_time_h1=5
online_time_m1=6
online_time_s1=0
online_time_h2=23
online_time_m2=59
online_time_s2=59
bid_mode=1              # Auction mode (business rule)
is_auction_auto=0
is_gain_reward=0        # Reward collection flag (business rule)
is_cangshen_lottery=0
gold_min=0
state=1                 # 1=enabled, 0=disabled
```

Key fields:
- `pid/gid`: Platform/Game ID for API queries
- `sid`: Server ID found by server selection logic
- `state`: Controls whether server is active (1) or disabled (0)
- `bid_mode`, `is_gain_reward`: Business rule controlled fields

### Advanced Features

**7. Scheduling System** (`ScheduleManager` + `QtSchedulerService`)
- **Auto Check Scheduling**: Configurable daily server checking (default 8:00 AM)
- **GameClient Scheduling**: Automated game client start/stop/restart
- **Upload Scheduling**: Timed file uploads to remote servers
- **Time Synchronization**: Ensures accurate scheduling across time zones

**8. Process Management** (`GameClientController` + `ProcessController`)
- **GameClient.exe Lifecycle Management**: Start, stop, restart game processes
- **Process Detection**: Find processes by name with working directory context
- **Thread-Safe Operations**: Prevents conflicts during concurrent process operations
- **Automatic Process Recovery**: Restart failed game clients

**9. File Upload System** (`UploadManager`)
- **Scheduled Uploads**: Automatic file uploads at configured times
- **Multiple Upload Targets**: Support for different upload URLs
- **Progress Tracking**: Real-time upload progress monitoring
- **Error Recovery**: Retry mechanisms for failed uploads

**10. System Integration**
- **Windows Startup Registration** (`WindowsStartupManager`): Add/remove from Windows startup
- **System Tray Service** (`QtTrayService`): Minimize to tray, double-click restore
- **Single Instance Control**: Prevents multiple app instances
- **Admin Privilege Management**: Automatic UAC elevation when needed

**11. Sequential Execution Engine** (`SequentialExecutor`)
- **Step-by-Step Operations**: Execute complex multi-step workflows
- **Progress Tracking**: Real-time step completion status
- **Error Handling**: Graceful failure handling with rollback capability
- **Cancellation Support**: User can cancel long-running operations

**12. Performance Optimization**
- **Smart Caching**: File search cache with TTL and size limits
- **Connection Pooling**: Thread-safe HTTP connection management
- **Memory Management**: Automatic memory optimization and monitoring
- **Lazy Loading**: Deferred loading of non-critical components

## Development Guidelines

### Code Organization
- Place business logic in `core/` modules
- Use `services/` for external integrations and complex operations
- Put reusable UI components in `gui/`
- Add utility functions to appropriate `utils/` modules

### Threading Best Practices
- Always use `PyQt6ThreadManager` for background operations
- Use `SafeUIUpdater.call_in_main_thread()` for UI updates from worker threads
- Set proper cancel events for long-running operations

### Configuration Management
- Use `AppConfig` for application-level settings
- Use `ConfigurationService` for complex state management with signals
- Store per-file configurations in normalized path format
- All configurations are JSON-based with automatic migration

### Performance Considerations
- Use `@monitor_performance` decorator on performance-critical functions
- Implement proper resource cleanup in service destructors
- Use connection pooling for network operations
- Consider lazy loading for non-critical components
- Smart encoding detection for INI files (UTF-8, GBK, GB2312)

### Error Handling
- Use structured logging through `ui_coordinator.log()`
- Provide meaningful error messages to users
- Implement graceful degradation for non-critical failures
- Use proper exception handling in all service methods
- Support for cancel operations in long-running tasks

### Business Logic Patterns
- **PID/GID Pair Management**: Each `server.ini` can have multiple PID/GID pairs
- **Server Lifecycle Rules**: Automatically disable servers older than N days
- **Time-based Server Selection**: Find servers opening exactly N days in the future
- **Conditional Server Management**: Apply business rules like `is_gain_reward=0`

## Windows-Specific Features

### System Integration
- **Admin Privileges**: Automatic UAC elevation when needed
- **Single Instance**: Prevents multiple app instances
- **Startup Registration**: Windows startup integration
- **System Tray**: Minimize to tray functionality

### Process Control
- **GameClient Management**: Start/stop/restart GameClient.exe processes
- **Process Monitoring**: Track running game client instances
- **Scheduled Operations**: Automated file checking and client management

The application is designed specifically for Windows environments and leverages Windows APIs through pywin32 for deep system integration.

## Important Notes

### Game Business Context
This tool is specifically designed for a **Chinese web-based game platform** that uses:
- **PID (Platform ID)**: Identifies different game platforms (e.g., 37wan, 4399, etc.)
- **GID (Game ID)**: Identifies specific game instances/servers within a platform
- **Server Lifecycle Management**: New servers open daily, old servers are automatically disabled
- **Timezone Sensitivity**: All server times are in UTC+8 (China Standard Time)

### File Location Pattern
The tool searches for `server.ini` files in specific patterns:
- Target pattern: `**/settings/server.ini`
- Uses recursive search across all drives
- Caches search results with TTL for performance

### API Integration
- **Base URL**: `https://api.t5game.5jli.com/serverselect/loginserverdata/`
- **Parameters**: `pid`, `gid`, `version=99`, `platCode=37wan`, `account=1`
- **Response**: JSON with server list including `serverId` and `autoOpenTime`
- **Rate Limiting**: Built-in connection pooling to prevent API overload

### No Unit Tests
The project currently **does not include unit tests** - all testing is done manually through the GUI application.